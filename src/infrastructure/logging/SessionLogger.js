const fs = require('fs-extra');
const path = require('path');
const chalk = require('chalk');
const LogManager = require('./LogManager');

/**
 * 会话日志记录器 - 专门处理AI修复会话的完整日志记录
 * 包括会话开始/结束、文件修复过程、错误记录等
 */
class SessionLogger {
  constructor(options = {}) {
    this.logManager = new LogManager(options);
    this.sessionId = this._generateSessionId();
    this.sessionStartTime = new Date();
    this.sessionData = {
      sessionId: this.sessionId,
      startTime: this.sessionStartTime.toISOString(),
      endTime: null,
      projectPath: options.projectPath || process.cwd(),
      options: { ...options },
      phases: [], // 记录各个阶段
      files: {}, // 记录处理的文件
      attempts: [], // 记录所有尝试
      errors: [], // 记录错误
      statistics: {
        totalAttempts: 0,
        filesAnalyzed: 0,
        filesModified: 0,
        errorsFixed: 0,
        aiCalls: 0,
        totalDuration: 0
      }
    };

    // 会话状态
    this.currentPhase = null;
    this.currentAttempt = 0;

    console.log(chalk.blue(`📝 会话日志器已启动 (ID: ${this.sessionId.substring(0, 8)})`));
  }

  /**
   * 生成会话ID
   * @private
   */
  _generateSessionId() {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-').split('T')[0];
    const randomId = Math.random().toString(36).substring(2, 8);
    return `session-${timestamp}-${randomId}`;
  }

  /**
   * 开始新阶段
   * @param {string} phaseName - 阶段名称
   * @param {Object} metadata - 阶段元数据
   */
  startPhase(phaseName, metadata = {}) {
    const phase = {
      name: phaseName,
      startTime: new Date().toISOString(),
      endTime: null,
      metadata,
      duration: 0,
      success: null,
      result: null
    };

    this.sessionData.phases.push(phase);
    this.currentPhase = phase;

    if (this.logManager.options.enableDebugLog) {
      console.log(chalk.gray(`🎬 开始阶段: ${phaseName}`));
    }

    return phase;
  }

  /**
   * 结束当前阶段
   * @param {boolean} success - 阶段是否成功
   * @param {any} result - 阶段结果
   */
  endPhase(success = true, result = null) {
    if (!this.currentPhase) {
      console.warn(chalk.yellow('⚠️  没有正在进行的阶段'));
      return;
    }

    const endTime = new Date();
    this.currentPhase.endTime = endTime.toISOString();
    this.currentPhase.duration = endTime.getTime() - new Date(this.currentPhase.startTime).getTime();
    this.currentPhase.success = success;
    this.currentPhase.result = result;

    // 只在verbose模式下显示阶段结束信息，减少冗余输出
    if (this.logManager.options.enableDebugLog && this.logManager.options.verbose) {
      const status = success ? '✅ 成功' : '❌ 失败';
      console.log(chalk.gray(`🎬 结束阶段: ${this.currentPhase.name} ${status} (${this.currentPhase.duration}ms)`));
    }

    this.currentPhase = null;
  }

  /**
   * 开始新的修复尝试
   * @param {number} attemptNumber - 尝试次数
   * @param {Object} context - 上下文信息
   */
  startAttempt(attemptNumber, context = {}) {
    this.currentAttempt = attemptNumber;
    
    const attempt = {
      attemptNumber,
      startTime: new Date().toISOString(),
      endTime: null,
      context,
      phases: [],
      files: [],
      aiCalls: [],
      errors: [],
      success: null,
      result: null,
      duration: 0
    };

    this.sessionData.attempts.push(attempt);
    this.sessionData.statistics.totalAttempts++;

    console.log(chalk.cyan(`🔄 开始第 ${attemptNumber} 次修复尝试`));
    return attempt;
  }

  /**
   * 结束当前修复尝试
   * @param {boolean} success - 是否成功
   * @param {Object} result - 尝试结果
   */
  endAttempt(success = false, result = null) {
    const currentAttempt = this.getCurrentAttempt();
    if (!currentAttempt) {
      console.warn(chalk.yellow('⚠️  没有正在进行的修复尝试'));
      return;
    }

    const endTime = new Date();
    currentAttempt.endTime = endTime.toISOString();
    currentAttempt.duration = endTime.getTime() - new Date(currentAttempt.startTime).getTime();
    currentAttempt.success = success;
    currentAttempt.result = result;

    const status = success ? '✅ 成功' : '❌ 失败';
    console.log(chalk.cyan(`🔄 结束第 ${this.currentAttempt} 次修复尝试 ${status} (${currentAttempt.duration}ms)`));
  }

  /**
   * 获取当前修复尝试
   * @returns {Object|null} 当前尝试对象
   */
  getCurrentAttempt() {
    return this.sessionData.attempts.find(attempt => 
      attempt.attemptNumber === this.currentAttempt && !attempt.endTime
    );
  }

  /**
   * 记录AI调用
   * @param {Object} aiCallData - AI调用数据
   */
  logAICall(aiCallData) {
    const currentAttempt = this.getCurrentAttempt();
    
    const enrichedCallData = {
      ...aiCallData,
      sessionId: this.sessionId,
      attemptNumber: this.currentAttempt,
      phase: this.currentPhase?.name || 'unknown',
      timestamp: new Date().toISOString()
    };

    if (currentAttempt) {
      currentAttempt.aiCalls.push(enrichedCallData);
    }

    this.sessionData.statistics.aiCalls++;

    // 异步保存AI调用日志，只在verbose模式下显示错误
    this._saveAICallLog(enrichedCallData).catch(error => {
      if (this.logManager.options.verbose) {
        console.warn(chalk.yellow(`⚠️  保存AI调用日志失败: ${error.message}`));
      }
    });
  }

  /**
   * 保存AI调用日志
   * @private
   */
  async _saveAICallLog(aiCallData) {
    const logFileName = this.logManager.generateLogFileName(
      'ai-call',
      aiCallData.taskType || 'unknown',
      aiCallData.phase || 'unknown',
      aiCallData.attemptNumber || 1
    );

    return await this.logManager.writeLogFile(logFileName, aiCallData);
  }

  /**
   * 记录文件处理
   * @param {string} filePath - 文件路径
   * @param {string} action - 操作类型 (analyze, modify, backup等)
   * @param {Object} metadata - 元数据
   */
  logFileAction(filePath, action, metadata = {}) {
    const currentAttempt = this.getCurrentAttempt();
    
    const fileAction = {
      filePath,
      action,
      timestamp: new Date().toISOString(),
      attemptNumber: this.currentAttempt,
      phase: this.currentPhase?.name || 'unknown',
      metadata
    };

    // 更新会话文件记录
    if (!this.sessionData.files[filePath]) {
      this.sessionData.files[filePath] = {
        filePath,
        actions: [],
        firstSeen: fileAction.timestamp,
        lastModified: null,
        status: 'processing'
      };
    }

    this.sessionData.files[filePath].actions.push(fileAction);
    
    if (action === 'modify') {
      this.sessionData.files[filePath].lastModified = fileAction.timestamp;
      this.sessionData.files[filePath].status = 'modified';
      this.sessionData.statistics.filesModified++;
    }

    if (currentAttempt) {
      currentAttempt.files.push(fileAction);
    }

    if (action === 'analyze') {
      this.sessionData.statistics.filesAnalyzed++;
    }
  }

  /**
   * 记录错误
   * @param {string} errorType - 错误类型
   * @param {string} message - 错误信息
   * @param {Object} details - 错误详情
   */
  logError(errorType, message, details = {}) {
    const currentAttempt = this.getCurrentAttempt();
    
    const errorRecord = {
      type: errorType,
      message,
      details,
      timestamp: new Date().toISOString(),
      attemptNumber: this.currentAttempt,
      phase: this.currentPhase?.name || 'unknown',
      sessionId: this.sessionId
    };

    this.sessionData.errors.push(errorRecord);
    
    if (currentAttempt) {
      currentAttempt.errors.push(errorRecord);
    }

    console.log(chalk.red(`❌ 记录错误 [${errorType}]: ${message}`));
  }

  /**
   * 更新统计信息
   * @param {Object} stats - 统计数据
   */
  updateStatistics(stats) {
    Object.assign(this.sessionData.statistics, stats);
  }

  /**
   * 结束会话并生成最终报告
   * @param {boolean} success - 会话是否成功
   * @param {Object} finalResult - 最终结果
   * @returns {Promise<string>} 报告文件路径
   */
  async endSession(success = false, finalResult = null) {
    const endTime = new Date();
    this.sessionData.endTime = endTime.toISOString();
    this.sessionData.statistics.totalDuration = endTime.getTime() - this.sessionStartTime.getTime();
    this.sessionData.success = success;
    this.sessionData.finalResult = finalResult;

    // 生成会话摘要
    const summary = this._generateSessionSummary();
    this.sessionData.summary = summary;

    // 保存完整会话日志
    const sessionLogFileName = `${this.sessionId}-complete.json`;
    await this.logManager.writeLogFile(sessionLogFileName, this.sessionData);

    // 生成并保存会话报告
    const reportPath = await this._generateSessionReport();

    const status = success ? '✅ 成功' : '❌ 失败';
    const duration = Math.round(this.sessionData.statistics.totalDuration / 1000);
    
    console.log(chalk.blue(`📊 会话结束 ${status} (总时长: ${duration}s)`));
    console.log(chalk.blue(`📄 会话报告: ${path.basename(reportPath)}`));

    return reportPath;
  }

  /**
   * 生成会话摘要
   * @private
   */
  _generateSessionSummary() {
    const stats = this.sessionData.statistics;
    const attempts = this.sessionData.attempts;
    
    return {
      sessionOverview: {
        duration: `${Math.round(stats.totalDuration / 1000)}s`,
        attempts: stats.totalAttempts,
        aiCalls: stats.aiCalls,
        filesAnalyzed: stats.filesAnalyzed,
        filesModified: stats.filesModified,
        errorsFixed: stats.errorsFixed,
        totalErrors: this.sessionData.errors.length
      },
      phasesSummary: this.sessionData.phases.map(phase => ({
        name: phase.name,
        duration: `${Math.round(phase.duration / 1000)}s`,
        success: phase.success
      })),
      attemptsSummary: attempts.map(attempt => ({
        attemptNumber: attempt.attemptNumber,
        duration: `${Math.round(attempt.duration / 1000)}s`,
        aiCalls: attempt.aiCalls.length,
        filesProcessed: attempt.files.length,
        errors: attempt.errors.length,
        success: attempt.success
      })),
      filesProcessed: Object.keys(this.sessionData.files).length,
      mostActiveFiles: this._getTopActiveFiles(5)
    };
  }

  /**
   * 获取最活跃的文件
   * @private
   */
  _getTopActiveFiles(limit = 5) {
    return Object.values(this.sessionData.files)
      .map(file => ({
        path: file.filePath,
        actions: file.actions.length,
        lastModified: file.lastModified,
        status: file.status
      }))
      .sort((a, b) => b.actions - a.actions)
      .slice(0, limit);
  }

  /**
   * 生成会话报告
   * @private
   */
  async _generateSessionReport() {
    const reportFileName = `${this.sessionId}-report.json`;
    const report = {
      reportType: 'session-report',
      generatedAt: new Date().toISOString(),
      sessionId: this.sessionId,
      summary: this.sessionData.summary,
      recommendations: this._generateRecommendations(),
      metadata: {
        version: '1.0.0',
        generator: 'SessionLogger'
      }
    };

    await this.logManager.writeLogFile(reportFileName, report);
    return this.logManager.getLogFilePath(reportFileName);
  }

  /**
   * 生成建议和推荐
   * @private
   */
  _generateRecommendations() {
    const recommendations = [];
    const stats = this.sessionData.statistics;
    const errors = this.sessionData.errors;

    // 基于统计数据的建议
    if (stats.totalAttempts > 3) {
      recommendations.push({
        type: 'performance',
        message: '修复尝试次数较多，建议检查项目配置或寻求人工干预',
        priority: 'medium'
      });
    }

    if (stats.aiCalls > stats.totalAttempts * 3) {
      recommendations.push({
        type: 'efficiency',
        message: 'AI调用次数较多，可能需要优化修复策略',
        priority: 'low'
      });
    }

    // 基于错误的建议
    const errorTypes = errors.reduce((acc, error) => {
      acc[error.type] = (acc[error.type] || 0) + 1;
      return acc;
    }, {});

    if (errorTypes['build-error'] > 2) {
      recommendations.push({
        type: 'build',
        message: '构建错误频繁，建议检查项目依赖和配置',
        priority: 'high'
      });
    }

    return recommendations;
  }

  /**
   * 获取会话状态
   * @returns {Object} 当前会话状态
   */
  getSessionStatus() {
    return {
      sessionId: this.sessionId,
      startTime: this.sessionData.startTime,
      currentPhase: this.currentPhase?.name || null,
      currentAttempt: this.currentAttempt,
      statistics: { ...this.sessionData.statistics },
      isActive: !this.sessionData.endTime
    };
  }

  /**
   * 获取会话日志文件
   * @returns {Promise<Array>} 相关日志文件列表
   */
  async getSessionLogFiles() {
    return await this.logManager.searchLogs(this.sessionId, { 
      limit: 100,
      includeContent: false 
    });
  }
}

module.exports = SessionLogger;
