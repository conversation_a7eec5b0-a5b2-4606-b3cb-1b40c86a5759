/**
 * Vue 转换器
 * 提供自定义的 Vue 2 到 Vue 3 代码转换规则
 */
class CustomVueTransformer {
  /**
   * 应用自定义 Vue 转换规则
   * @param {string} code - 源代码
   * @returns {string} 转换后的代码
   */
  transform(code) {
    code = code.replace(
      /import\s+{\s*\$on,\s*\$off,\s*\$once,\s*\$emit\s*}\s+from\s+['"]utils\/gogocodeTransfer['"]/g,
      'import { $on, $off, $once, $emit } from \'@/utils/gogocodeTransfer\''
    )

    // 处理其他可能的 gogocodeTransfer 导入格式
    code = code.replace(
      /import\s+{\s*\$on,\s*\$off,\s*\$once,\s*\$emit\s*}\s+from\s+['"]\.\/utils\/gogocodeTransfer['"]/g,
      'import { $on, $off, $once, $emit } from \'@/utils/gogocodeTransfer\''
    )

    code = code.replace(
      /import\s+{\s*\$on,\s*\$off,\s*\$once,\s*\$emit\s*}\s+from\s+['"]\.\.\/utils\/gogocodeTransfer['"]/g,
      'import { $on, $off, $once, $emit } from \'@/utils/gogocodeTransfer\''
    )

    code = code.replace(
      /import\s+{\s*\$on,\s*\$off,\s*\$once,\s*\$emit\s*}\s+from\s+['"]\.\.\/\.\.\/utils\/gogocodeTransfer['"]/g,
      'import { $on, $off, $once, $emit } from \'@/utils/gogocodeTransfer\''
    )

    // 处理更复杂的相对路径
    code = code.replace(
      /import\s+{\s*\$on,\s*\$off,\s*\$once,\s*\$emit\s*}\s+from\s+['"]\.\.\/\.\.\/\.\.\/utils\/gogocodeTransfer['"]/g,
      'import { $on, $off, $once, $emit } from \'@/utils/gogocodeTransfer\''
    )

    // 替换 Element UI 导入
    code = code.replace(
      /import\s+{\s*([^}]+)\s*}\s+from\s+['"]element-ui['\"]/g,
      (match, imports) => {
        // 清理导入列表中的多余空格
        const cleanImports = imports.replace(/\s+/g, ' ').trim()
        return `import { ${cleanImports} } from 'element-plus'`
      }
    )

    // 替换 Element UI 完整导入
    code = code.replace(
      /import\s+ElementUI\s+from\s+['"]element-ui['\"]/g,
      'import ElementPlus from \'element-plus\''
    )

    // 替换 Element UI CSS 导入
    code = code.replace(
      /import\s+['"]element-ui\/lib\/theme-chalk\/index\.css['\"]/g,
      'import \'element-plus/dist/index.css\''
    )

    return code
  }
}

module.exports = CustomVueTransformer
